"""
PayFast Payment Gateway Routes for EduFair Platform

This module provides API endpoints for PayFast payment integration including:
- Event payment creation
- Subscription payment creation
- Webhook handling
- Payment status checking
- Refund processing
"""

from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Request, BackgroundTasks
from sqlalchemy.orm import Session
from uuid import UUID
import logging

# Import services and dependencies
from services.PayFastService import PayFastService
from config.session import get_db
from config.security import oauth2_scheme
from config.deps import get_current_user

# Import schemas
from Schemas.PayFast import (
    PayFastEventPaymentRequest,
    PayFastSubscriptionPaymentRequest,
    PayFastPaymentResponse,
    PayFastSubscriptionPaymentResponse,
    PayFastWebhookNotification,
    PayFastWebhookResponse,
    PayFastRefundRequest,
    PayFastRefundResponse,
    PayFastPaymentStatusResponse,
    PayFastConfigResponse,
    PayFastFormHelper
)

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()
payfast_service = PayFastService()


# ===== EVENT PAYMENT ENDPOINTS =====

@router.post("/events/payment", response_model=PayFastPaymentResponse)
def create_event_payment(
    payment_request: PayFastEventPaymentRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Create PayFast payment for event registration"""
    
    current_user = get_current_user(token, db)
    
    try:
        payment_result = payfast_service.create_event_payment(
            db=db,
            registration_id=payment_request.registration_id,
            amount=payment_request.amount,
            currency=payment_request.currency,
            user_email=payment_request.user_email or current_user.email,
            user_name=payment_request.user_name or f"{current_user.first_name} {current_user.last_name}"
        )
        
        return PayFastPaymentResponse(**payment_result)
        
    except Exception as e:
        logger.error(f"Error creating PayFast event payment: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/events/payment/{payment_id}/form", response_model=PayFastFormHelper)
def get_event_payment_form(
    payment_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get PayFast payment form data for frontend integration"""
    
    current_user = get_current_user(token, db)
    
    try:
        payment_status = payfast_service.get_payment_status(db, payment_id)
        
        if not payment_status.gateway_response:
            raise HTTPException(status_code=404, detail="Payment form data not found")
        
        return PayFastFormHelper(
            action_url=payfast_service.base_url,
            method="POST",
            form_data=payment_status.gateway_response
        )
        
    except Exception as e:
        logger.error(f"Error getting PayFast payment form: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


# ===== SUBSCRIPTION PAYMENT ENDPOINTS =====

@router.post("/subscriptions/payment", response_model=PayFastSubscriptionPaymentResponse)
def create_subscription_payment(
    payment_request: PayFastSubscriptionPaymentRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Create PayFast payment for subscription"""
    
    current_user = get_current_user(token, db)
    
    try:
        payment_result = payfast_service.create_subscription_payment(
            db=db,
            subscription_id=payment_request.subscription_id,
            amount=payment_request.amount,
            currency=payment_request.currency,
            user_email=payment_request.user_email or current_user.email,
            user_name=payment_request.user_name or f"{current_user.first_name} {current_user.last_name}"
        )
        
        return PayFastSubscriptionPaymentResponse(**payment_result)
        
    except Exception as e:
        logger.error(f"Error creating PayFast subscription payment: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


# ===== WEBHOOK ENDPOINTS =====

@router.post("/webhook", response_model=PayFastWebhookResponse)
async def payfast_webhook(
    request: Request,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Handle PayFast webhook notifications"""
    
    try:
        # Get form data from PayFast
        form_data = await request.form()
        notification_data = dict(form_data)
        
        logger.info(f"Received PayFast webhook: {notification_data}")
        
        # Process webhook in background to respond quickly to PayFast
        background_tasks.add_task(
            process_webhook_background,
            db,
            notification_data
        )
        
        # Respond immediately to PayFast
        return PayFastWebhookResponse(
            status="received",
            message="Webhook received and will be processed"
        )
        
    except Exception as e:
        logger.error(f"Error processing PayFast webhook: {str(e)}")
        raise HTTPException(status_code=400, detail="Webhook processing failed")


def process_webhook_background(db: Session, notification_data: Dict[str, Any]):
    """Process PayFast webhook in background"""
    
    try:
        result = payfast_service.process_webhook_notification(db, notification_data)
        logger.info(f"PayFast webhook processed successfully: {result}")
        
    except Exception as e:
        logger.error(f"Error in background webhook processing: {str(e)}")


# ===== PAYMENT STATUS ENDPOINTS =====

@router.get("/payment/{payment_id}/status", response_model=PayFastPaymentStatusResponse)
def get_payment_status(
    payment_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get PayFast payment status"""
    
    current_user = get_current_user(token, db)
    
    try:
        payment_status = payfast_service.get_payment_status(db, payment_id)
        
        return PayFastPaymentStatusResponse(
            payment_id=payment_status.id,
            payfast_payment_id=payment_status.gateway_payment_intent_id,
            status=payment_status.status,
            amount=payment_status.amount,
            currency=payment_status.currency,
            created_at=payment_status.created_at,
            processed_at=payment_status.processed_at,
            failed_at=payment_status.failed_at,
            failure_reason=payment_status.failure_reason,
            gateway_response=payment_status.gateway_response
        )
        
    except Exception as e:
        logger.error(f"Error getting PayFast payment status: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


# ===== REFUND ENDPOINTS =====

@router.post("/payment/{payment_id}/refund", response_model=PayFastRefundResponse)
def refund_payment(
    payment_id: UUID,
    refund_request: PayFastRefundRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Process PayFast payment refund"""
    
    current_user = get_current_user(token, db)
    
    # Only allow admins or the payment owner to refund
    # This should be enhanced with proper permission checking
    
    try:
        refund_result = payfast_service.refund_payment(
            db=db,
            payment_id=payment_id,
            refund_amount=refund_request.refund_amount,
            reason=refund_request.reason
        )
        
        return PayFastRefundResponse(**refund_result)
        
    except Exception as e:
        logger.error(f"Error processing PayFast refund: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


# ===== CONFIGURATION ENDPOINTS =====

@router.get("/config", response_model=PayFastConfigResponse)
def get_payfast_config():
    """Get PayFast configuration information"""
    
    return PayFastConfigResponse(
        is_sandbox=payfast_service.is_sandbox,
        merchant_id=payfast_service.merchant_id,
        payment_url=payfast_service.base_url,
        supported_currencies=["ZAR", "USD", "EUR", "GBP"],
        webhook_url=f"{payfast_service.base_url}/api/payments/payfast/webhook"
    )


# ===== TESTING ENDPOINTS (Sandbox only) =====

@router.post("/test/payment")
def create_test_payment(
    db: Session = Depends(get_db)
):
    """Create a test payment for PayFast sandbox testing"""
    
    if not payfast_service.is_sandbox:
        raise HTTPException(status_code=403, detail="Test payments only available in sandbox mode")
    
    try:
        # Create a test payment form
        test_payment_form = payfast_service.create_payment_form_data(
            amount=100.00,
            item_name="Test Payment",
            item_description="Test payment for PayFast integration",
            custom_str1="test_payment",
            custom_str2="sandbox_test",
            custom_str3="",
            email_address="<EMAIL>",
            name_first="Test",
            name_last="User"
        )
        
        return {
            "message": "Test payment created",
            "payment_url": test_payment_form['payment_url'],
            "payment_data": test_payment_form['payment_data'],
            "note": "This is a sandbox test payment"
        }
        
    except Exception as e:
        logger.error(f"Error creating test payment: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
