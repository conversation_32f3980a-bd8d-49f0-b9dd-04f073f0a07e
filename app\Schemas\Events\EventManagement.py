"""
Event Management Schemas for EduFair Platform

This module contains all Pydantic schemas for the Event Management system including:
- Event Categories (Workshops, Conferences, Webinars, Competitions)
- Event Locations (Physical and Virtual)
- Guest/Speaker Profiles
- Event Tickets and Pricing
- Event Registrations
- Payment Gateway Integration
- Calendar Views
- Event Analytics and Feedback
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator
from datetime import datetime
from uuid import UUID
from decimal import Decimal

# Import Enums
from Models.Events import (
    EventStatusEnum, TicketStatusEnum, RegistrationStatusEnum, 
    PaymentStatusEnum, EventCategoryEnum, PaymentGatewayEnum
)


# ===== EVENT CATEGORY SCHEMAS =====

class EventCategoryBase(BaseModel):
    name: str = Field(..., max_length=100, description="Category name (workshop, conference, webinar, competition)")
    display_name: str = Field(..., max_length=100, description="Display name for UI")
    description: Optional[str] = Field(None, description="Category description")
    color: Optional[str] = Field(None, max_length=7, description="Hex color code for UI")
    icon: Optional[str] = Field(None, max_length=50, description="Icon name or class")
    banner_image_url: Optional[str] = Field(None, max_length=500, description="Category banner for sliders")
    is_active: bool = Field(True, description="Whether category is active")
    sort_order: int = Field(0, description="Sort order for display")


class EventCategoryCreate(EventCategoryBase):
    pass


class EventCategoryUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=100)
    display_name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = None
    color: Optional[str] = Field(None, max_length=7)
    icon: Optional[str] = Field(None, max_length=50)
    banner_image_url: Optional[str] = Field(None, max_length=500)
    is_active: Optional[bool] = None
    sort_order: Optional[int] = None


class EventCategoryOut(EventCategoryBase):
    id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# ===== EVENT LOCATION SCHEMAS =====

class EventLocationBase(BaseModel):
    name: str = Field(..., max_length=200, description="Location name")
    description: Optional[str] = Field(None, description="Location description")
    address: Optional[str] = Field(None, description="Full address")
    city: Optional[str] = Field(None, max_length=100)
    state: Optional[str] = Field(None, max_length=100)
    country: Optional[str] = Field(None, max_length=100)
    postal_code: Optional[str] = Field(None, max_length=20)
    latitude: Optional[Decimal] = Field(None, description="Latitude coordinate")
    longitude: Optional[Decimal] = Field(None, description="Longitude coordinate")
    capacity: Optional[int] = Field(None, ge=1, description="Venue capacity")
    facilities: Optional[List[str]] = Field(None, description="Available facilities")
    contact_info: Optional[Dict[str, str]] = Field(None, description="Contact information")
    images: Optional[List[str]] = Field(None, description="Location images")
    is_virtual: bool = Field(False, description="Whether location is virtual")
    virtual_platform: Optional[str] = Field(None, max_length=100, description="Virtual platform (Zoom, Teams, etc.)")
    virtual_link: Optional[str] = Field(None, max_length=500, description="Virtual meeting link")
    virtual_meeting_id: Optional[str] = Field(None, max_length=100, description="Meeting ID")
    virtual_passcode: Optional[str] = Field(None, max_length=50, description="Meeting passcode")
    is_active: bool = Field(True, description="Whether location is active")


class EventLocationCreate(EventLocationBase):
    pass


class EventLocationUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=200)
    description: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = Field(None, max_length=100)
    state: Optional[str] = Field(None, max_length=100)
    country: Optional[str] = Field(None, max_length=100)
    postal_code: Optional[str] = Field(None, max_length=20)
    latitude: Optional[Decimal] = None
    longitude: Optional[Decimal] = None
    capacity: Optional[int] = Field(None, ge=1)
    facilities: Optional[List[str]] = None
    contact_info: Optional[Dict[str, str]] = None
    images: Optional[List[str]] = None
    is_virtual: Optional[bool] = None
    virtual_platform: Optional[str] = Field(None, max_length=100)
    virtual_link: Optional[str] = Field(None, max_length=500)
    virtual_meeting_id: Optional[str] = Field(None, max_length=100)
    virtual_passcode: Optional[str] = Field(None, max_length=50)
    is_active: Optional[bool] = None


class EventLocationOut(EventLocationBase):
    id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# ===== EVENT SPEAKER/GUEST SCHEMAS =====

class EventSpeakerBase(BaseModel):
    name: str = Field(..., max_length=200, description="Speaker name")
    title: Optional[str] = Field(None, max_length=200, description="Professional title")
    bio: Optional[str] = Field(None, description="Speaker biography")
    profile_image_url: Optional[str] = Field(None, max_length=500, description="Profile image URL")
    company: Optional[str] = Field(None, max_length=200, description="Company/Organization")
    position: Optional[str] = Field(None, max_length=200, description="Position at company")
    website: Optional[str] = Field(None, max_length=500, description="Personal website")
    linkedin_url: Optional[str] = Field(None, max_length=500, description="LinkedIn profile")
    twitter_url: Optional[str] = Field(None, max_length=500, description="Twitter profile")
    instagram_url: Optional[str] = Field(None, max_length=500, description="Instagram profile")
    facebook_url: Optional[str] = Field(None, max_length=500, description="Facebook profile")
    email: Optional[str] = Field(None, max_length=255, description="Contact email")
    phone: Optional[str] = Field(None, max_length=20, description="Contact phone")
    expertise_areas: Optional[List[str]] = Field(None, description="Areas of expertise")
    achievements: Optional[List[str]] = Field(None, description="Notable achievements")
    certifications: Optional[List[str]] = Field(None, description="Professional certifications")
    speaking_topics: Optional[List[str]] = Field(None, description="Topics they speak about")
    languages: Optional[List[str]] = Field(None, description="Languages they speak")
    is_featured: bool = Field(False, description="Whether speaker is featured")
    is_keynote_speaker: bool = Field(False, description="Whether speaker is keynote")
    speaker_fee: Optional[Decimal] = Field(None, ge=0, description="Speaking fee")
    travel_required: bool = Field(False, description="Whether travel is required")
    rating: Optional[Decimal] = Field(None, ge=0, le=5, description="Average rating")
    total_events: int = Field(0, ge=0, description="Total events spoken at")


class EventSpeakerCreate(EventSpeakerBase):
    pass


class EventSpeakerUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=200)
    title: Optional[str] = Field(None, max_length=200)
    bio: Optional[str] = None
    profile_image_url: Optional[str] = Field(None, max_length=500)
    company: Optional[str] = Field(None, max_length=200)
    position: Optional[str] = Field(None, max_length=200)
    website: Optional[str] = Field(None, max_length=500)
    linkedin_url: Optional[str] = Field(None, max_length=500)
    twitter_url: Optional[str] = Field(None, max_length=500)
    instagram_url: Optional[str] = Field(None, max_length=500)
    facebook_url: Optional[str] = Field(None, max_length=500)
    email: Optional[str] = Field(None, max_length=255)
    phone: Optional[str] = Field(None, max_length=20)
    expertise_areas: Optional[List[str]] = None
    achievements: Optional[List[str]] = None
    certifications: Optional[List[str]] = None
    speaking_topics: Optional[List[str]] = None
    languages: Optional[List[str]] = None
    is_featured: Optional[bool] = None
    is_keynote_speaker: Optional[bool] = None
    speaker_fee: Optional[Decimal] = Field(None, ge=0)
    travel_required: Optional[bool] = None
    rating: Optional[Decimal] = Field(None, ge=0, le=5)
    total_events: Optional[int] = Field(None, ge=0)


class EventSpeakerOut(EventSpeakerBase):
    id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# ===== EVENT SCHEMAS =====

class EventBase(BaseModel):
    title: str = Field(..., max_length=300, description="Event title")
    description: Optional[str] = Field(None, description="Event description")
    short_description: Optional[str] = Field(None, max_length=500, description="Short description for cards")
    banner_image_url: Optional[str] = Field(None, max_length=500, description="Event banner image")
    gallery_images: Optional[List[str]] = Field(None, description="Gallery images for slider")
    start_datetime: datetime = Field(..., description="Event start time")
    end_datetime: datetime = Field(..., description="Event end time")
    registration_start: Optional[datetime] = Field(None, description="Registration start time")
    registration_end: Optional[datetime] = Field(None, description="Registration end time")
    category_id: UUID = Field(..., description="Event category ID")
    location_id: Optional[UUID] = Field(None, description="Event location ID")
    status: EventStatusEnum = Field(EventStatusEnum.DRAFT, description="Event status")
    is_featured: bool = Field(False, description="Whether event is featured in sliders")
    is_public: bool = Field(True, description="Whether event is public")
    requires_approval: bool = Field(False, description="Whether registration requires approval")
    max_attendees: Optional[int] = Field(None, ge=1, description="Maximum attendees")
    min_attendees: Optional[int] = Field(None, ge=1, description="Minimum attendees")
    agenda: Optional[List[Dict[str, Any]]] = Field(None, description="Event agenda/schedule")
    requirements: Optional[str] = Field(None, description="Prerequisites or requirements")
    tags: Optional[List[str]] = Field(None, description="Event tags")
    external_links: Optional[Dict[str, str]] = Field(None, description="External links")
    is_competition: bool = Field(False, description="Whether event is a competition")
    competition_exam_id: Optional[UUID] = Field(None, description="Linked exam ID (competitions reuse exam system)")
    competition_rules: Optional[str] = Field(None, description="Competition rules and guidelines")
    prize_details: Optional[Dict[str, Any]] = Field(None, description="Prize information and awards")

    # Competition settings (inherited from exam system) - DISABLED: columns don't exist in database
    # auto_grading_enabled: bool = Field(True, description="Enable automatic grading")
    # manual_review_required: bool = Field(False, description="Require manual review by mentors")
    # proctoring_enabled: bool = Field(False, description="Enable proctoring during competition")
    # randomize_questions: bool = Field(True, description="Randomize question order")


class EventCreate(EventBase):
    speaker_ids: Optional[List[UUID]] = Field(None, description="List of speaker IDs")


class EventUpdate(BaseModel):
    title: Optional[str] = Field(None, max_length=300)
    description: Optional[str] = None
    short_description: Optional[str] = Field(None, max_length=500)
    banner_image_url: Optional[str] = Field(None, max_length=500)
    gallery_images: Optional[List[str]] = None
    start_datetime: Optional[datetime] = None
    end_datetime: Optional[datetime] = None
    registration_start: Optional[datetime] = None
    registration_end: Optional[datetime] = None
    category_id: Optional[UUID] = None
    location_id: Optional[UUID] = None
    status: Optional[EventStatusEnum] = None
    is_featured: Optional[bool] = None
    is_public: Optional[bool] = None
    requires_approval: Optional[bool] = None
    max_attendees: Optional[int] = Field(None, ge=1)
    min_attendees: Optional[int] = Field(None, ge=1)
    agenda: Optional[List[Dict[str, Any]]] = None
    requirements: Optional[str] = None
    tags: Optional[List[str]] = None
    external_links: Optional[Dict[str, str]] = None
    is_competition: Optional[bool] = None
    competition_exam_id: Optional[UUID] = None
    competition_rules: Optional[str] = None
    prize_details: Optional[Dict[str, Any]] = None
    # Competition settings - DISABLED: columns don't exist in database
    # auto_grading_enabled: Optional[bool] = None
    # manual_review_required: Optional[bool] = None
    # proctoring_enabled: Optional[bool] = None
    # randomize_questions: Optional[bool] = None
    speaker_ids: Optional[List[UUID]] = None


class EventOut(EventBase):
    id: UUID
    organizer_id: UUID
    institute_id: UUID
    total_registrations: int
    total_attendees: int
    average_rating: Optional[Decimal]
    total_reviews: int
    created_at: datetime
    updated_at: datetime

    # Related data
    category: Optional[EventCategoryOut] = None
    location: Optional[EventLocationOut] = None
    speakers: Optional[List[EventSpeakerOut]] = None

    class Config:
        from_attributes = True


# ===== EVENT TICKET SCHEMAS =====

class EventTicketBase(BaseModel):
    name: str = Field(..., max_length=200, description="Ticket type name (e.g., Early Bird, VIP, Student)")
    description: Optional[str] = Field(None, description="Ticket description")
    price: Decimal = Field(..., ge=0, description="Ticket price")
    currency: str = Field("PKR", max_length=3, description="Currency code")
    total_quantity: Optional[int] = Field(None, ge=1, description="Total available tickets (null = unlimited)")
    status: TicketStatusEnum = Field(TicketStatusEnum.ACTIVE, description="Ticket status")
    sale_start: Optional[datetime] = Field(None, description="Ticket sale start time")
    sale_end: Optional[datetime] = Field(None, description="Ticket sale end time")
    min_quantity_per_order: int = Field(1, ge=1, description="Minimum tickets per order")
    max_quantity_per_order: Optional[int] = Field(None, ge=1, description="Maximum tickets per order")
    requires_approval: bool = Field(False, description="Whether ticket requires approval")
    is_transferable: bool = Field(True, description="Whether ticket is transferable")
    is_refundable: bool = Field(False, description="Whether ticket is refundable")
    refund_policy: Optional[str] = Field(None, description="Refund policy details")
    terms_and_conditions: Optional[str] = Field(None, description="Terms and conditions")
    benefits: Optional[List[str]] = Field(None, description="Ticket benefits/features")
    includes: Optional[List[str]] = Field(None, description="What's included with ticket")


class EventTicketCreate(EventTicketBase):
    event_id: UUID = Field(..., description="Event ID")


class EventTicketUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=200)
    description: Optional[str] = None
    price: Optional[Decimal] = Field(None, ge=0)
    currency: Optional[str] = Field(None, max_length=3)
    total_quantity: Optional[int] = Field(None, ge=1)
    status: Optional[TicketStatusEnum] = None
    sale_start: Optional[datetime] = None
    sale_end: Optional[datetime] = None
    min_quantity_per_order: Optional[int] = Field(None, ge=1)
    max_quantity_per_order: Optional[int] = Field(None, ge=1)
    requires_approval: Optional[bool] = None
    is_transferable: Optional[bool] = None
    is_refundable: Optional[bool] = None
    refund_policy: Optional[str] = None
    terms_and_conditions: Optional[str] = None
    benefits: Optional[List[str]] = None
    includes: Optional[List[str]] = None


class EventTicketOut(EventTicketBase):
    id: UUID
    event_id: UUID
    sold_quantity: int
    available_quantity: Optional[int]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# ===== EVENT REGISTRATION SCHEMAS =====

class EventRegistrationBase(BaseModel):
    quantity: int = Field(1, ge=1, description="Number of tickets")
    attendee_info: Optional[Dict[str, Any]] = Field(None, description="Additional attendee information")
    special_requirements: Optional[str] = Field(None, description="Special requirements")
    emergency_contact: Optional[Dict[str, str]] = Field(None, description="Emergency contact information")
    dietary_preferences: Optional[List[str]] = Field(None, description="Dietary preferences for catering")
    accessibility_needs: Optional[str] = Field(None, description="Accessibility requirements")


class EventRegistrationCreate(EventRegistrationBase):
    event_id: UUID = Field(..., description="Event ID")
    ticket_id: Optional[UUID] = Field(None, description="Ticket ID (null for free events)")


class EventRegistrationUpdate(BaseModel):
    status: Optional[RegistrationStatusEnum] = None
    attendee_info: Optional[Dict[str, Any]] = None
    special_requirements: Optional[str] = None
    emergency_contact: Optional[Dict[str, str]] = None
    dietary_preferences: Optional[List[str]] = None
    accessibility_needs: Optional[str] = None


class EventRegistrationOut(EventRegistrationBase):
    id: UUID
    event_id: UUID
    ticket_id: Optional[UUID]
    user_id: UUID
    registration_number: str
    status: RegistrationStatusEnum
    total_amount: Decimal
    currency: str
    payment_status: PaymentStatusEnum
    payment_reference: Optional[str]
    payment_method: Optional[str]
    qr_code: Optional[str]
    check_in_code: Optional[str]
    registered_at: datetime
    confirmed_at: Optional[datetime]
    cancelled_at: Optional[datetime]
    attended_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    # Related data
    event: Optional[EventOut] = None
    ticket: Optional[EventTicketOut] = None

    class Config:
        from_attributes = True


# ===== EVENT PAYMENT SCHEMAS =====

class EventPaymentBase(BaseModel):
    amount: Decimal = Field(..., gt=0, description="Payment amount")
    currency: str = Field("PKR", max_length=3, description="Currency code")
    payment_method: PaymentGatewayEnum = Field(..., description="Payment gateway method")
    payment_description: Optional[str] = Field(None, max_length=500, description="Payment description")
    customer_email: Optional[str] = Field(None, max_length=255, description="Customer email")
    customer_phone: Optional[str] = Field(None, max_length=20, description="Customer phone")


class EventPaymentCreate(EventPaymentBase):
    event_id: UUID = Field(..., description="Event ID")
    registration_id: UUID = Field(..., description="Registration ID")


class EventPaymentUpdate(BaseModel):
    status: Optional[PaymentStatusEnum] = None
    gateway_transaction_id: Optional[str] = Field(None, max_length=255)
    gateway_payment_intent_id: Optional[str] = Field(None, max_length=255)
    gateway_session_id: Optional[str] = Field(None, max_length=255)
    processed_at: Optional[datetime] = None
    failed_at: Optional[datetime] = None
    failure_reason: Optional[str] = None
    gateway_response: Optional[Dict[str, Any]] = None


class EventPaymentOut(EventPaymentBase):
    id: UUID
    event_id: UUID
    registration_id: UUID
    user_id: UUID
    status: PaymentStatusEnum
    payment_gateway: Optional[str]
    gateway_transaction_id: Optional[str]
    gateway_payment_intent_id: Optional[str]
    gateway_session_id: Optional[str]
    initiated_at: datetime
    processed_at: Optional[datetime]
    failed_at: Optional[datetime]
    refunded_at: Optional[datetime]
    failure_reason: Optional[str]
    refund_reason: Optional[str]
    refund_amount: Optional[Decimal]
    gateway_response: Optional[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# ===== EVENT FEEDBACK SCHEMAS =====

class EventFeedbackBase(BaseModel):
    overall_rating: Optional[int] = Field(None, ge=1, le=5, description="Overall rating (1-5)")
    review: Optional[str] = Field(None, description="Written review")
    would_recommend: Optional[bool] = Field(None, description="Would recommend to others")
    content_rating: Optional[int] = Field(None, ge=1, le=5, description="Content quality rating")
    speaker_rating: Optional[int] = Field(None, ge=1, le=5, description="Speaker rating")
    venue_rating: Optional[int] = Field(None, ge=1, le=5, description="Venue rating")
    organization_rating: Optional[int] = Field(None, ge=1, le=5, description="Organization rating")
    networking_rating: Optional[int] = Field(None, ge=1, le=5, description="Networking opportunities rating")
    value_for_money_rating: Optional[int] = Field(None, ge=1, le=5, description="Value for money rating")
    best_aspects: Optional[List[str]] = Field(None, description="What they liked most")
    improvement_suggestions: Optional[str] = Field(None, description="Suggestions for improvement")
    future_topics_interest: Optional[List[str]] = Field(None, description="Topics they'd like to see in future")
    is_public: bool = Field(True, description="Whether feedback is public")


class EventFeedbackCreate(EventFeedbackBase):
    event_id: UUID = Field(..., description="Event ID")


class EventFeedbackUpdate(BaseModel):
    overall_rating: Optional[int] = Field(None, ge=1, le=5)
    review: Optional[str] = None
    would_recommend: Optional[bool] = None
    content_rating: Optional[int] = Field(None, ge=1, le=5)
    speaker_rating: Optional[int] = Field(None, ge=1, le=5)
    venue_rating: Optional[int] = Field(None, ge=1, le=5)
    organization_rating: Optional[int] = Field(None, ge=1, le=5)
    networking_rating: Optional[int] = Field(None, ge=1, le=5)
    value_for_money_rating: Optional[int] = Field(None, ge=1, le=5)
    best_aspects: Optional[List[str]] = None
    improvement_suggestions: Optional[str] = None
    future_topics_interest: Optional[List[str]] = None
    is_public: Optional[bool] = None
    is_approved: Optional[bool] = None


class EventFeedbackOut(EventFeedbackBase):
    id: UUID
    event_id: UUID
    user_id: UUID
    is_approved: bool
    submitted_at: datetime
    approved_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# ===== CALENDAR SCHEMAS =====

class CalendarEventOut(BaseModel):
    id: UUID
    title: str
    start_datetime: datetime
    end_datetime: datetime
    category_id: UUID
    category_name: str
    category_color: Optional[str]
    location_name: Optional[str]
    is_virtual: bool
    is_featured: bool
    banner_image_url: Optional[str]
    short_description: Optional[str]
    registration_start: Optional[datetime]
    registration_end: Optional[datetime]
    status: EventStatusEnum

    class Config:
        from_attributes = True


class CalendarResponse(BaseModel):
    events: List[CalendarEventOut]
    total_count: int
    month: int
    year: int


# ===== LIST AND FILTER SCHEMAS =====

class EventListFilter(BaseModel):
    category_id: Optional[UUID] = None
    location_id: Optional[UUID] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    is_featured: Optional[bool] = None
    is_virtual: Optional[bool] = None
    status: Optional[EventStatusEnum] = None
    search: Optional[str] = None
    tags: Optional[List[str]] = None
    price_min: Optional[Decimal] = None
    price_max: Optional[Decimal] = None


class EventListResponse(BaseModel):
    events: List[EventOut]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool


class UpcomingEventsResponse(BaseModel):
    featured_events: List[EventOut]
    upcoming_events: List[EventOut]
    categories: List[EventCategoryOut]


# ===== ANALYTICS SCHEMAS =====

class EventAnalyticsOut(BaseModel):
    event_id: UUID
    total_views: int
    total_registrations: int
    total_cancellations: int
    total_attendees: int
    total_no_shows: int
    total_revenue: Decimal
    total_refunds: Decimal
    net_revenue: Decimal
    average_rating: Optional[Decimal]
    total_feedback_count: int
    recommendation_rate: Optional[Decimal]
    peak_registration_date: Optional[datetime]
    last_updated: datetime

    class Config:
        from_attributes = True


# ===== COMPETITION INTEGRATION SCHEMAS =====
# Note: Competitions reuse the existing Exam system models and schemas
# These schemas provide competition-specific views and operations

class CompetitionEventOut(EventOut):
    """Extended event schema for competition events with exam integration"""

    # Competition-specific fields
    competition_exam: Optional[Dict[str, Any]] = None  # Exam details from Exam model
    total_participants: int = 0
    total_submissions: int = 0
    competition_status: str = "upcoming"  # upcoming, active, completed, cancelled

    # Leaderboard preview (top 3)
    top_performers: Optional[List[Dict[str, Any]]] = None

    # Competition statistics
    average_score: Optional[Decimal] = None
    highest_score: Optional[Decimal] = None
    completion_rate: Optional[Decimal] = None

    class Config:
        from_attributes = True


class CompetitionCreateRequest(BaseModel):
    """Request to create a competition event with integrated exam"""

    # Event details
    event_data: EventCreate

    # Exam integration
    exam_title: str = Field(..., description="Title for the competition exam")
    exam_description: Optional[str] = Field(None, description="Exam description")
    exam_duration_minutes: int = Field(..., gt=0, description="Exam duration in minutes")
    exam_instructions: Optional[str] = Field(None, description="Exam instructions")

    # Question generation (reuse existing AI system)
    generate_questions_with_ai: bool = Field(False, description="Generate questions using AI")
    ai_generation_prompt: Optional[str] = Field(None, description="AI prompt for question generation")
    question_count: Optional[int] = Field(None, ge=1, le=100, description="Number of questions to generate")
    difficulty_distribution: Optional[Dict[str, int]] = Field(None, description="Distribution of question difficulties")

    # Manual questions
    question_ids: Optional[List[UUID]] = Field(None, description="Pre-existing question IDs to include")


class CompetitionParticipantOut(BaseModel):
    """Competition participant with exam attempt details"""

    user_id: UUID
    username: str
    full_name: str
    profile_image_url: Optional[str]

    # Registration details
    registration_id: UUID
    registered_at: datetime

    # Exam attempt details (from StudentExamAttempt)
    attempt_id: Optional[UUID] = None
    attempt_status: Optional[str] = None  # not_started, in_progress, completed, submitted
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    time_taken_minutes: Optional[int] = None

    # Results (from StudentExamAttempt)
    total_score: Optional[Decimal] = None
    percentage_score: Optional[Decimal] = None
    rank: Optional[int] = None

    # Competition-specific
    awards: Optional[List[str]] = None
    certificate_url: Optional[str] = None

    class Config:
        from_attributes = True


class CompetitionLeaderboardOut(BaseModel):
    """Competition leaderboard using exam results"""

    competition_id: UUID
    competition_title: str
    total_participants: int

    # Leaderboard entries
    participants: List[CompetitionParticipantOut]

    # Statistics
    average_score: Optional[Decimal]
    highest_score: Optional[Decimal]
    completion_rate: Decimal

    # Pagination
    page: int
    page_size: int
    total_pages: int

    class Config:
        from_attributes = True


class CompetitionAttemptRequest(BaseModel):
    """Request to start a competition attempt (reuses exam attempt system)"""

    competition_id: UUID = Field(..., description="Competition event ID")
    # Note: This will create a StudentExamAttempt record using the competition's linked exam


class CompetitionSubmissionRequest(BaseModel):
    """Request to submit competition answers (reuses exam submission system)"""

    attempt_id: UUID = Field(..., description="Exam attempt ID")
    answers: List[Dict[str, Any]] = Field(..., description="List of answers (reuses StudentExamAnswer format)")
    # Note: This will create StudentExamAnswer records and trigger the same grading process as exams
