# PayFast Payment Gateway Integration

## Overview

PayFast is a South African payment gateway that has been integrated into the EduFair platform to handle payments for event registrations and subscriptions. The integration supports both sandbox (testing) and production environments.

## Features

### ✅ Implemented Features

1. **Event Payment Processing**
   - Create PayFast payments for event ticket purchases
   - Handle payment confirmations via webhooks
   - Support for multiple currencies (ZAR, USD, EUR, GBP)
   - Automatic registration confirmation on successful payment

2. **Subscription Payment Processing**
   - Create PayFast payments for subscription renewals
   - Handle subscription activation via webhooks
   - Support for recurring billing workflows

3. **Webhook Integration**
   - Secure webhook handling with signature verification
   - Background processing for webhook notifications
   - Automatic payment status updates

4. **Payment Management**
   - Payment status tracking
   - Refund processing (manual via PayFast dashboard)
   - Payment history and analytics

5. **Security Features**
   - MD5 signature verification
   - Secure webhook processing
   - Environment-based configuration

## Configuration

### Environment Variables

Add the following variables to your `.env` file:

```env
# PayFast Payment Gateway Configuration
PAYFAST_MERCHANT_ID="your_merchant_id"
PAYFAST_MERCHANT_KEY="your_merchant_key"
PAYFAST_PASSPHRASE="your_passphrase"  # Optional but recommended
PAYFAST_SANDBOX_URL="https://sandbox.payfast.co.za/eng/process"
PAYFAST_SANDBOX="true"  # Set to "false" for production

# Application URLs for PayFast callbacks
FRONTEND_URL="http://localhost:3000"
BACKEND_URL="http://localhost:8000"
```

### Current Configuration (Sandbox)

The system is currently configured for sandbox testing with:
- Merchant ID: `10041646`
- Merchant Key: `z1vak5tt19cqd`
- Sandbox URL: `https://sandbox.payfast.co.za/eng/process`

## API Endpoints

### Event Payments

#### Create Event Payment
```http
POST /api/payments/payfast/events/payment
Authorization: Bearer {token}
Content-Type: application/json

{
  "registration_id": "uuid",
  "amount": 100.00,
  "currency": "ZAR",
  "user_email": "<EMAIL>",
  "user_name": "John Doe"
}
```

#### Get Payment Form
```http
GET /api/payments/payfast/events/payment/{payment_id}/form
Authorization: Bearer {token}
```

### Subscription Payments

#### Create Subscription Payment
```http
POST /api/payments/payfast/subscriptions/payment
Authorization: Bearer {token}
Content-Type: application/json

{
  "subscription_id": "uuid",
  "amount": 299.00,
  "currency": "ZAR",
  "user_email": "<EMAIL>",
  "user_name": "John Doe"
}
```

### Payment Status

#### Get Payment Status
```http
GET /api/payments/payfast/payment/{payment_id}/status
Authorization: Bearer {token}
```

### Webhooks

#### PayFast Webhook (No Authentication Required)
```http
POST /api/payments/payfast/webhook
Content-Type: application/x-www-form-urlencoded

# PayFast sends form data with payment notification
```

### Refunds

#### Process Refund
```http
POST /api/payments/payfast/payment/{payment_id}/refund
Authorization: Bearer {token}
Content-Type: application/json

{
  "refund_amount": 50.00,  # Optional, full refund if not specified
  "reason": "Customer requested refund"
}
```

## Integration Workflow

### Event Payment Flow

1. **User Registration**
   - User registers for an event
   - Registration created with `payment_status = "pending"`

2. **Payment Creation**
   - Frontend calls `/api/payments/payfast/events/payment`
   - PayFast payment form data is generated
   - Payment record created in database

3. **Payment Processing**
   - User is redirected to PayFast payment page
   - User completes payment on PayFast
   - PayFast sends webhook notification

4. **Payment Confirmation**
   - Webhook processes payment notification
   - Payment status updated to "completed"
   - Registration confirmed automatically

### Frontend Integration

#### React Component Example

```jsx
import React, { useState } from 'react';

const PayFastPayment = ({ registrationId, amount }) => {
  const [paymentData, setPaymentData] = useState(null);
  const [loading, setLoading] = useState(false);

  const createPayment = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/payments/payfast/events/payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          registration_id: registrationId,
          amount: amount,
          currency: 'ZAR'
        })
      });
      
      const data = await response.json();
      setPaymentData(data);
    } catch (error) {
      console.error('Payment creation failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const submitPayment = () => {
    if (paymentData) {
      // Create and submit PayFast form
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = paymentData.payment_url;
      
      Object.entries(paymentData.payment_data).forEach(([key, value]) => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = value;
        form.appendChild(input);
      });
      
      document.body.appendChild(form);
      form.submit();
    }
  };

  return (
    <div>
      {!paymentData ? (
        <button onClick={createPayment} disabled={loading}>
          {loading ? 'Creating Payment...' : 'Pay with PayFast'}
        </button>
      ) : (
        <button onClick={submitPayment}>
          Proceed to PayFast (R{amount})
        </button>
      )}
    </div>
  );
};
```

## Testing

### Sandbox Testing

1. **Test Card Details**
   - Use PayFast sandbox environment
   - Test with various payment scenarios
   - Verify webhook processing

2. **Test Endpoints**
   ```http
   POST /api/payments/payfast/test/payment
   ```

### Webhook Testing

1. **Use ngrok for local testing**
   ```bash
   ngrok http 8000
   ```

2. **Update webhook URL in PayFast dashboard**
   ```
   https://your-ngrok-url.ngrok.io/api/payments/payfast/webhook
   ```

## Security Considerations

1. **Signature Verification**
   - All webhook notifications are verified using MD5 signatures
   - Invalid signatures are rejected

2. **Environment Separation**
   - Sandbox and production environments are clearly separated
   - Different merchant credentials for each environment

3. **Data Protection**
   - Sensitive payment data is not stored locally
   - Only payment references and statuses are maintained

## Error Handling

### Common Error Scenarios

1. **Invalid Payment Data**
   - Malformed requests return 400 Bad Request
   - Detailed error messages for debugging

2. **Payment Failures**
   - Failed payments are marked appropriately
   - Users can retry payments

3. **Webhook Issues**
   - Invalid webhooks are logged and rejected
   - Background processing prevents timeout issues

## Monitoring and Logging

### Payment Tracking

- All payment attempts are logged
- Payment status changes are tracked
- Webhook notifications are logged for debugging

### Analytics

- Payment success/failure rates
- Revenue tracking by event/subscription
- Payment method preferences

## Production Deployment

### Checklist

1. **Update Environment Variables**
   ```env
   PAYFAST_SANDBOX="false"
   PAYFAST_MERCHANT_ID="production_merchant_id"
   PAYFAST_MERCHANT_KEY="production_merchant_key"
   ```

2. **Configure Webhook URLs**
   - Update PayFast dashboard with production webhook URL
   - Ensure HTTPS is enabled

3. **Test Production Environment**
   - Verify payment processing
   - Test webhook delivery
   - Confirm email notifications

## Support and Troubleshooting

### Common Issues

1. **Webhook Not Received**
   - Check PayFast dashboard for delivery status
   - Verify webhook URL is accessible
   - Check server logs for errors

2. **Payment Status Not Updated**
   - Verify webhook signature validation
   - Check payment ID mapping
   - Review database transaction logs

3. **Refund Processing**
   - PayFast refunds require manual processing
   - Use PayFast merchant dashboard
   - Update local records manually if needed

### Contact Information

- PayFast Support: <EMAIL>
- PayFast Documentation: https://developers.payfast.co.za/
- EduFair Development Team: [Your contact information]
