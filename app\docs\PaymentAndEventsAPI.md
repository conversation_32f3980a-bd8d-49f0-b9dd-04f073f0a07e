# EduFair Payment & Events API Documentation

## Overview

This document provides comprehensive API documentation for the EduFair payment system and event management. It covers PayFast payment integration and event management endpoints for frontend developers.

**Base URL:** `http://localhost:8000/api`

**Authentication:** All endpoints require Bear<PERSON> token authentication unless specified otherwise.

```
Authorization: Bearer <your_jwt_token>
```

---

## 🎫 Event Management API

### 1. Get All Events

**Endpoint:** `GET /events`

**Description:** Retrieve a list of all available events (excluding competitions).

**Headers:**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `category` (optional): Filter by event category
- `status` (optional): Filter by event status (upcoming, ongoing, completed)

**Response Schema:**
```json
{
  "events": [
    {
      "id": "uuid",
      "title": "string",
      "description": "string",
      "category": "workshop|seminar|conference|training",
      "start_date": "2024-01-15T10:00:00Z",
      "end_date": "2024-01-15T18:00:00Z",
      "location": "string",
      "venue_address": "string",
      "max_participants": 100,
      "current_participants": 45,
      "registration_fee": 299.99,
      "currency": "ZAR",
      "status": "upcoming|ongoing|completed|cancelled",
      "registration_deadline": "2024-01-10T23:59:59Z",
      "organizer_id": "uuid",
      "organizer_name": "string",
      "image_url": "string",
      "tags": ["education", "technology"],
      "requirements": "string",
      "agenda": "string",
      "created_at": "2024-01-01T12:00:00Z",
      "updated_at": "2024-01-01T12:00:00Z"
    }
  ],
  "total": 25,
  "page": 1,
  "limit": 10,
  "total_pages": 3
}
```

### 2. Get Event Details

**Endpoint:** `GET /events/{event_id}`

**Description:** Get detailed information about a specific event.

**Path Parameters:**
- `event_id`: UUID of the event

**Response Schema:**
```json
{
  "id": "uuid",
  "title": "Advanced Web Development Workshop",
  "description": "Learn modern web development techniques...",
  "category": "workshop",
  "start_date": "2024-01-15T10:00:00Z",
  "end_date": "2024-01-15T18:00:00Z",
  "location": "Tech Hub Conference Center",
  "venue_address": "123 Innovation Street, Cape Town",
  "max_participants": 50,
  "current_participants": 32,
  "registration_fee": 599.99,
  "currency": "ZAR",
  "status": "upcoming",
  "registration_deadline": "2024-01-10T23:59:59Z",
  "organizer": {
    "id": "uuid",
    "name": "TechEd Institute",
    "email": "<EMAIL>",
    "phone": "+27123456789"
  },
  "image_url": "https://example.com/event-image.jpg",
  "tags": ["web-development", "javascript", "react"],
  "requirements": "Basic programming knowledge required",
  "agenda": "10:00-12:00 Introduction to React...",
  "speakers": [
    {
      "name": "John Doe",
      "title": "Senior Developer",
      "bio": "Expert in React and Node.js"
    }
  ],
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z"
}
```

### 3. Register for Event

**Endpoint:** `POST /events/{event_id}/register`

**Description:** Register the authenticated user for an event.

**Path Parameters:**
- `event_id`: UUID of the event

**Request Body:**
```json
{
  "additional_info": "Dietary requirements: Vegetarian",
  "emergency_contact": {
    "name": "Jane Doe",
    "phone": "+27987654321",
    "relationship": "Spouse"
  }
}
```

**Response Schema:**
```json
{
  "registration_id": "uuid",
  "event_id": "uuid",
  "user_id": "uuid",
  "registration_date": "2024-01-05T14:30:00Z",
  "payment_status": "pending",
  "payment_amount": 599.99,
  "currency": "ZAR",
  "status": "pending_payment",
  "qr_code": "base64_encoded_qr_code",
  "confirmation_number": "EVT-2024-001234",
  "additional_info": "Dietary requirements: Vegetarian",
  "emergency_contact": {
    "name": "Jane Doe",
    "phone": "+27987654321",
    "relationship": "Spouse"
  }
}
```

### 4. Get User's Event Registrations

**Endpoint:** `GET /events/my-registrations`

**Description:** Get all event registrations for the authenticated user.

**Query Parameters:**
- `status` (optional): Filter by registration status
- `page` (optional): Page number
- `limit` (optional): Items per page

**Response Schema:**
```json
{
  "registrations": [
    {
      "registration_id": "uuid",
      "event": {
        "id": "uuid",
        "title": "Advanced Web Development Workshop",
        "start_date": "2024-01-15T10:00:00Z",
        "location": "Tech Hub Conference Center"
      },
      "registration_date": "2024-01-05T14:30:00Z",
      "payment_status": "completed",
      "status": "confirmed",
      "confirmation_number": "EVT-2024-001234",
      "qr_code": "base64_encoded_qr_code"
    }
  ],
  "total": 5,
  "page": 1,
  "limit": 10
}
```

---

## 💳 PayFast Payment API

### 1. Create Event Payment

**Endpoint:** `POST /payments/payfast/events/payment`

**Description:** Create a PayFast payment for event registration.

**Request Body:**
```json
{
  "registration_id": "uuid",
  "amount": 599.99,
  "currency": "ZAR",
  "user_email": "<EMAIL>",
  "user_name": "John Doe",
  "return_url": "https://yourapp.com/payment/success",
  "cancel_url": "https://yourapp.com/payment/cancel"
}
```

**Response Schema:**
```json
{
  "payment_id": "uuid",
  "payfast_payment_id": "unique_payfast_id",
  "payment_url": "https://sandbox.payfast.co.za/eng/process",
  "payment_data": {
    "merchant_id": "10041646",
    "merchant_key": "z1vak5tt19cqd",
    "return_url": "https://yourapp.com/payment/success",
    "cancel_url": "https://yourapp.com/payment/cancel",
    "notify_url": "http://localhost:8000/api/payments/payfast/webhook",
    "name_first": "John",
    "name_last": "Doe",
    "email_address": "<EMAIL>",
    "m_payment_id": "unique_payfast_id",
    "amount": "599.99",
    "item_name": "Event Registration - Advanced Web Development Workshop",
    "item_description": "Registration for event (ID: event_uuid)",
    "custom_str1": "payment_uuid",
    "custom_str2": "registration_uuid",
    "custom_str3": "event_payment",
    "signature": "generated_md5_signature"
  },
  "status": "pending"
}
```

### 2. Get Payment Form Data

**Endpoint:** `GET /payments/payfast/events/payment/{payment_id}/form`

**Description:** Get PayFast payment form data for frontend integration.

**Path Parameters:**
- `payment_id`: UUID of the payment

**Response Schema:**
```json
{
  "action_url": "https://sandbox.payfast.co.za/eng/process",
  "method": "POST",
  "form_data": {
    "merchant_id": "10041646",
    "merchant_key": "z1vak5tt19cqd",
    "return_url": "https://yourapp.com/payment/success",
    "cancel_url": "https://yourapp.com/payment/cancel",
    "notify_url": "http://localhost:8000/api/payments/payfast/webhook",
    "name_first": "John",
    "name_last": "Doe",
    "email_address": "<EMAIL>",
    "m_payment_id": "unique_payfast_id",
    "amount": "599.99",
    "item_name": "Event Registration - Advanced Web Development Workshop",
    "item_description": "Registration for event",
    "signature": "generated_md5_signature"
  }
}
```

### 3. Create Subscription Payment

**Endpoint:** `POST /payments/payfast/subscriptions/payment`

**Description:** Create a PayFast payment for subscription.

**Request Body:**
```json
{
  "subscription_id": "uuid",
  "amount": 299.99,
  "currency": "ZAR",
  "user_email": "<EMAIL>",
  "user_name": "John Doe",
  "return_url": "https://yourapp.com/payment/success",
  "cancel_url": "https://yourapp.com/payment/cancel"
}
```

**Response Schema:**
```json
{
  "payfast_payment_id": "unique_payfast_id",
  "payment_url": "https://sandbox.payfast.co.za/eng/process",
  "payment_data": {
    "merchant_id": "10041646",
    "merchant_key": "z1vak5tt19cqd",
    "return_url": "https://yourapp.com/payment/success",
    "cancel_url": "https://yourapp.com/payment/cancel",
    "notify_url": "http://localhost:8000/api/payments/payfast/webhook",
    "name_first": "John",
    "name_last": "Doe",
    "email_address": "<EMAIL>",
    "m_payment_id": "unique_payfast_id",
    "amount": "299.99",
    "item_name": "Subscription - Premium Plan",
    "item_description": "Subscription payment",
    "signature": "generated_md5_signature"
  },
  "status": "pending"
}
```

### 4. Get Payment Status

**Endpoint:** `GET /payments/payfast/payment/{payment_id}/status`

**Description:** Check the status of a PayFast payment.

**Path Parameters:**
- `payment_id`: UUID of the payment

**Response Schema:**
```json
{
  "payment_id": "uuid",
  "payfast_payment_id": "unique_payfast_id",
  "status": "pending|completed|failed|refunded",
  "amount": 599.99,
  "currency": "ZAR",
  "created_at": "2024-01-05T14:30:00Z",
  "processed_at": "2024-01-05T14:35:00Z",
  "failed_at": null,
  "failure_reason": null,
  "gateway_response": {
    "pf_payment_id": "payfast_transaction_id",
    "payment_status": "COMPLETE",
    "amount_gross": "599.99",
    "amount_fee": "13.79",
    "amount_net": "586.20"
  }
}
```

### 5. Process Payment Refund

**Endpoint:** `POST /payments/payfast/payment/{payment_id}/refund`

**Description:** Process a refund for a PayFast payment.

**Path Parameters:**
- `payment_id`: UUID of the payment

**Request Body:**
```json
{
  "refund_amount": 299.99,
  "reason": "Event cancelled by organizer"
}
```

**Response Schema:**
```json
{
  "payment_id": "uuid",
  "refund_amount": 299.99,
  "status": "refunded",
  "message": "Refund processed (manual verification required)",
  "note": "PayFast refunds require manual processing through the PayFast dashboard"
}
```

### 6. PayFast Configuration

**Endpoint:** `GET /payments/payfast/config`

**Description:** Get PayFast configuration information.

**Authentication:** Not required

**Response Schema:**
```json
{
  "is_sandbox": true,
  "merchant_id": "10041646",
  "payment_url": "https://sandbox.payfast.co.za/eng/process",
  "supported_currencies": ["ZAR", "USD", "EUR", "GBP"],
  "webhook_url": "http://localhost:8000/api/payments/payfast/webhook"
}
```

---

## 🔄 Payment Flow Integration

### Frontend Payment Integration Steps

1. **Create Payment:**
   ```javascript
   // Step 1: Create payment
   const paymentResponse = await fetch('/api/payments/payfast/events/payment', {
     method: 'POST',
     headers: {
       'Authorization': 'Bearer ' + token,
       'Content-Type': 'application/json'
     },
     body: JSON.stringify({
       registration_id: 'event_registration_uuid',
       amount: 599.99,
       currency: 'ZAR',
       user_email: '<EMAIL>',
       user_name: 'John Doe'
     })
   });
   
   const payment = await paymentResponse.json();
   ```

2. **Create PayFast Form:**
   ```javascript
   // Step 2: Create and submit PayFast form
   const form = document.createElement('form');
   form.method = 'POST';
   form.action = payment.payment_url;
   
   // Add all payment data as hidden inputs
   Object.entries(payment.payment_data).forEach(([key, value]) => {
     const input = document.createElement('input');
     input.type = 'hidden';
     input.name = key;
     input.value = value;
     form.appendChild(input);
   });
   
   document.body.appendChild(form);
   form.submit(); // Redirects to PayFast
   ```

3. **Handle Return URLs:**
   ```javascript
   // Success page: /payment/success
   // Check payment status
   const statusResponse = await fetch(`/api/payments/payfast/payment/${paymentId}/status`);
   const status = await statusResponse.json();
   
   if (status.status === 'completed') {
     // Payment successful - show confirmation
     // Registration is automatically confirmed via webhook
   }
   ```

---

## 📋 Error Handling

### Common Error Responses

**400 Bad Request:**
```json
{
  "detail": "Invalid request data",
  "errors": {
    "amount": ["Amount must be greater than 0"],
    "currency": ["Currency must be one of: ZAR, USD, EUR, GBP"]
  }
}
```

**401 Unauthorized:**
```json
{
  "detail": "Authentication required"
}
```

**404 Not Found:**
```json
{
  "detail": "Event not found"
}
```

**409 Conflict:**
```json
{
  "detail": "User already registered for this event"
}
```

**422 Validation Error:**
```json
{
  "detail": [
    {
      "loc": ["body", "amount"],
      "msg": "ensure this value is greater than 0",
      "type": "value_error.number.not_gt",
      "ctx": {"limit_value": 0}
    }
  ]
}
```

---

## 🔐 Authentication

All API endpoints (except PayFast config and webhook) require JWT authentication:

```javascript
const token = localStorage.getItem('access_token');

fetch('/api/events', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
})
```

---

## 📝 Notes for Frontend Developers

1. **Payment Flow:** Always create payment first, then redirect to PayFast using the provided form data.

2. **Webhook Handling:** Payment confirmations are processed automatically via webhooks. Check payment status on return URLs.

3. **Error Handling:** Implement proper error handling for all API calls with appropriate user feedback.

4. **Currency:** Default currency is ZAR (South African Rand). Other currencies supported: USD, EUR, GBP.

5. **Sandbox Mode:** Currently configured for sandbox testing. All payments are test transactions.

6. **QR Codes:** Event registrations include QR codes for check-in purposes.

7. **Real-time Updates:** Consider implementing WebSocket connections for real-time payment status updates.

8. **Security:** Never store sensitive payment data on the frontend. Always use the provided PayFast form submission method.

---

## 🎯 Event Status Codes

### Event Status Values
- `upcoming` - Event is scheduled for the future
- `ongoing` - Event is currently happening
- `completed` - Event has finished
- `cancelled` - Event has been cancelled

### Registration Status Values
- `pending_payment` - Registration created, payment pending
- `confirmed` - Payment completed, registration confirmed
- `cancelled` - Registration cancelled
- `attended` - User attended the event (marked via QR code)

### Payment Status Values
- `pending` - Payment created, awaiting processing
- `completed` - Payment successfully processed
- `failed` - Payment failed or declined
- `refunded` - Payment has been refunded

---

## 🔔 Webhook Information

### PayFast Webhook Endpoint
**Endpoint:** `POST /payments/payfast/webhook`

**Description:** This endpoint receives payment notifications from PayFast. It's automatically processed by the backend.

**Important Notes:**
- This endpoint does NOT require authentication
- PayFast sends form-encoded data, not JSON
- Webhook processing happens in the background
- Payment status and registration status are updated automatically

### Webhook Processing Flow
1. PayFast sends payment notification to webhook
2. Backend verifies PayFast signature
3. Payment status updated in database
4. If event payment: registration status updated to "confirmed"
5. If subscription payment: subscription activated
6. QR code generated for event check-in

---

## 🧪 Testing & Development

### Sandbox Testing
Current configuration uses PayFast sandbox environment:
- **Merchant ID:** 10041646
- **Merchant Key:** z1vak5tt19cqd
- **Environment:** Sandbox
- **Test URL:** https://sandbox.payfast.co.za/eng/process

### Test Payment Data
For sandbox testing, you can use these test scenarios:

**Successful Payment:**
- Use any valid email address
- Amount: Any positive value
- PayFast will simulate successful payment

**Failed Payment:**
- Use email: `<EMAIL>`
- PayFast will simulate failed payment

### Test Event Creation
Create test events for development:

```json
{
  "title": "Test Workshop",
  "description": "Test event for development",
  "category": "workshop",
  "start_date": "2024-12-01T10:00:00Z",
  "end_date": "2024-12-01T17:00:00Z",
  "location": "Test Venue",
  "venue_address": "123 Test Street",
  "max_participants": 50,
  "registration_fee": 100.00,
  "currency": "ZAR",
  "registration_deadline": "2024-11-25T23:59:59Z"
}
```

---

## 📊 Rate Limiting

### API Rate Limits
- **General endpoints:** 100 requests per minute per user
- **Payment endpoints:** 10 requests per minute per user
- **Webhook endpoint:** No rate limiting (PayFast controlled)

### Rate Limit Headers
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

---

## 🔍 Debugging & Troubleshooting

### Common Issues

**1. Payment Not Processing:**
- Check payment status via API
- Verify webhook URL is accessible
- Check PayFast dashboard for transaction details

**2. Registration Not Confirmed:**
- Webhook may have failed
- Check payment status first
- Manual confirmation may be required

**3. Invalid Signature Errors:**
- PayFast signature validation failed
- Check merchant credentials
- Verify webhook URL configuration

### Debug Endpoints

**Test Payment Creation (Sandbox Only):**
```
POST /payments/payfast/test/payment
```

**Check Webhook Status:**
```
GET /payments/payfast/config
```

---

## 📈 Monitoring & Analytics

### Payment Metrics
Track these metrics for payment monitoring:
- Payment success rate
- Average payment processing time
- Failed payment reasons
- Refund frequency

### Event Metrics
Monitor these event-related metrics:
- Registration conversion rate
- Payment completion rate
- Event attendance rate
- Popular event categories

---

## 🚀 Production Deployment

### Environment Variables for Production
```env
PAYFAST_MERCHANT_ID="your_production_merchant_id"
PAYFAST_MERCHANT_KEY="your_production_merchant_key"
PAYFAST_PASSPHRASE="your_production_passphrase"
PAYFAST_SANDBOX="false"
FRONTEND_URL="https://yourdomain.com"
BACKEND_URL="https://api.yourdomain.com"
```

### Production Checklist
- [ ] Update PayFast credentials to production values
- [ ] Set `PAYFAST_SANDBOX="false"`
- [ ] Configure production webhook URL in PayFast dashboard
- [ ] Test payment flow in production environment
- [ ] Set up monitoring and alerting
- [ ] Configure SSL certificates for webhook endpoint

### Security Considerations
- Use HTTPS for all webhook URLs
- Validate all PayFast signatures
- Log all payment transactions
- Implement fraud detection
- Regular security audits

---

## 📞 Support & Contact

### PayFast Support
- **Email:** <EMAIL>
- **Documentation:** https://developers.payfast.co.za/
- **Dashboard:** https://www.payfast.co.za/

### API Support
For API-related issues:
- Check server logs for detailed error messages
- Verify authentication tokens
- Ensure proper request formatting
- Test in sandbox environment first

This completes the comprehensive API documentation for PayFast payments and event management in the EduFair platform.
