"""
Test script to verify that admin endpoint returns actual image data instead of URLs.

This script tests:
1. Admin endpoint /api/institutes/admin/institute/{id} returns image data
2. Public endpoint /api/institutes/public/{id} returns image data
3. General endpoint /api/institutes/{id} returns image data
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import base64
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from main import app
from config.session import get_db
from Models.users import User, UserTypeEnum, InstituteProfile
from Schemas.Institute.Institute import InstituteProfileWithDocumentsRequest, ImageData
import uuid
import bcrypt

client = TestClient(app)

def create_test_users():
    """Create test institute user and admin user"""
    try:
        db = next(get_db())
        
        # Create institute user
        test_id = str(uuid.uuid4())[:8]
        institute_email = f"test_institute_admin_{test_id}@example.com"
        institute_username = f"test_institute_admin_{test_id}"
        institute_mobile = f"+123456789{test_id[:1]}"
        
        # Create admin user
        admin_email = f"test_admin_{test_id}@example.com"
        admin_username = f"test_admin_{test_id}"
        admin_mobile = f"+987654321{test_id[:1]}"
        
        # Clean up any existing test users
        existing_users = db.query(User).filter(
            (User.email.like(f"%test_institute_admin_{test_id}%")) | 
            (User.email.like(f"%test_admin_{test_id}%"))
        ).all()
        for user in existing_users:
            profile = db.query(InstituteProfile).filter(InstituteProfile.user_id == user.id).first()
            if profile:
                db.delete(profile)
            db.delete(user)
        db.commit()
        
        # Create institute user
        hashed_password = bcrypt.hashpw("TestPass123!".encode('utf-8'), bcrypt.gensalt())
        
        institute_user = User(
            id=uuid.uuid4(),
            username=institute_username,
            email=institute_email,
            mobile=institute_mobile,
            password_hash=hashed_password.decode('utf-8'),
            country="United States",
            user_type=UserTypeEnum.institute,
            is_email_verified=True,
            is_mobile_verified=True
        )
        
        # Create admin user
        admin_user = User(
            id=uuid.uuid4(),
            username=admin_username,
            email=admin_email,
            mobile=admin_mobile,
            password_hash=hashed_password.decode('utf-8'),
            country="United States",
            user_type=UserTypeEnum.admin,
            is_email_verified=True,
            is_mobile_verified=True
        )
        
        db.add(institute_user)
        db.add(admin_user)
        db.commit()
        db.refresh(institute_user)
        db.refresh(admin_user)
        
        print(f"✅ Created test institute user: {institute_user.id}")
        print(f"✅ Created test admin user: {admin_user.id}")
        
        # Generate auth tokens
        from config.security import create_access_token
        institute_token = create_access_token(data={"sub": institute_email})
        admin_token = create_access_token(data={"sub": admin_email})
        
        return institute_token, institute_user.id, admin_token, admin_user.id
        
    except Exception as e:
        print(f"❌ Error creating test users: {e}")
        db.rollback()
        return None, None, None, None
    finally:
        db.close()

def create_test_image_data(filename="test_image.png"):
    """Create test image data in base64 format"""
    # Create a simple test image content (PNG header + minimal data)
    png_header = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde'
    test_content = png_header + b'\x00' * 50  # Minimal PNG data
    
    return base64.b64encode(test_content).decode()

def test_admin_endpoint():
    """Test that admin endpoint returns actual image data"""
    print("🧪 Testing admin endpoint with image data...")
    
    # Create test users and get tokens
    institute_token, institute_id, admin_token, admin_id = create_test_users()
    if not institute_token or not admin_token:
        return False
    
    try:
        # First, create a profile with logo and banner as institute user
        institute_headers = {"Authorization": f"Bearer {institute_token}"}
        
        logo_data = ImageData(
            filename="admin_test_logo.png",
            content_type="image/png",
            data=create_test_image_data("admin_test_logo.png")
        )
        
        banner_data = ImageData(
            filename="admin_test_banner.png",
            content_type="image/png",
            data=create_test_image_data("admin_test_banner.png")
        )
        
        # Create profile with images
        request_data = InstituteProfileWithDocumentsRequest(
            institute_name="Test University for Admin",
            description="Testing admin endpoint image data",
            city="Admin Test City",
            institute_type="university",
            logo=logo_data,
            banner=banner_data
        ).model_dump()
        
        # Upload profile with images
        response = client.put(
            "/api/institutes/profile/with-documents",
            headers=institute_headers,
            json=request_data
        )
        
        if response.status_code != 200:
            print(f"❌ Failed to create profile: {response.status_code}: {response.text}")
            return False
        
        print("✅ Profile created successfully")
        
        # Now test the admin endpoint
        admin_headers = {"Authorization": f"Bearer {admin_token}"}
        
        response = client.get(
            f"/api/institutes/admin/institute/{institute_id}",
            headers=admin_headers
        )
        
        if response.status_code != 200:
            print(f"❌ Failed to get institute via admin endpoint: {response.status_code}: {response.text}")
            return False
        
        result = response.json()
        
        # Check that we have the expected structure
        if 'user' not in result or 'profile' not in result:
            print("❌ Response missing user or profile data")
            return False
        
        # Check logo data
        profile = result['profile']
        if 'logo' in profile and profile['logo'] and profile['logo']['data']:
            logo = profile['logo']
            print("✅ Admin endpoint: Logo data found in response")
            print(f"   Filename: {logo.get('filename')}")
            print(f"   Content Type: {logo.get('content_type')}")
            print(f"   Data Length: {len(logo['data'])} characters")
        else:
            print("❌ Admin endpoint: Logo data missing or empty")
            return False
        
        # Check banner data
        if 'banner' in profile and profile['banner'] and profile['banner']['data']:
            banner = profile['banner']
            print("✅ Admin endpoint: Banner data found in response")
            print(f"   Filename: {banner.get('filename')}")
            print(f"   Content Type: {banner.get('content_type')}")
            print(f"   Data Length: {len(banner['data'])} characters")
        else:
            print("❌ Admin endpoint: Banner data missing or empty")
            return False
        
        print("✅ Admin endpoint returns image data successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Admin endpoint test failed: {e}")
        return False

def cleanup_test_data():
    """Clean up test data"""
    try:
        db = next(get_db())
        
        # Delete test users and their profiles
        test_patterns = ["test_institute_admin_", "test_admin_"]
        for pattern in test_patterns:
            users = db.query(User).filter(User.email.like(f"%{pattern}%")).all()
            for user in users:
                profile = db.query(InstituteProfile).filter(InstituteProfile.user_id == user.id).first()
                if profile:
                    db.delete(profile)
                db.delete(user)
        
        db.commit()
        print("✅ Cleaned up test data")
        
    except Exception as e:
        print(f"⚠️ Warning: Could not clean up test data: {e}")
    finally:
        db.close()

def main():
    """Main test function"""
    print("🚀 Testing admin endpoint image data response...")
    print("=" * 60)
    
    success_count = 0
    total_tests = 1
    
    # Test: Admin endpoint with image data
    if test_admin_endpoint():
        success_count += 1
    
    print("\n" + "-" * 40)
    
    # Cleanup
    cleanup_test_data()
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("✅ All tests passed! Admin endpoint returns image data correctly.")
        return 0
    else:
        print("❌ Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    exit(main())
