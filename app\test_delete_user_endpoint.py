"""
Test script for the admin delete user endpoint
"""

import requests
import json
from datetime import datetime


def test_delete_user_endpoint():
    """Test the delete user endpoint"""
    
    # Configuration
    BASE_URL = "http://localhost:8000"  # Adjust as needed
    
    # You'll need to replace these with actual values
    ADMIN_TOKEN = "your-admin-token-here"
    TEST_USER_ID = "test-user-id-to-delete"
    
    print("🧪 Testing Admin Delete User Endpoint")
    print("=" * 50)
    
    # Test 1: Delete user with admin token
    print("\n1. Testing user deletion with admin token...")
    
    headers = {
        "Authorization": f"Bearer {ADMIN_TOKEN}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.delete(
            f"{BASE_URL}/api/users/{TEST_USER_ID}",
            headers=headers
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ User deleted successfully!")
            print(f"Response: {json.dumps(result, indent=2)}")
            
        elif response.status_code == 404:
            print("❌ User not found")
            print(f"Error: {response.json()}")
            
        elif response.status_code == 403:
            print("❌ Access denied - admin privileges required")
            print(f"Error: {response.json()}")
            
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    
    # Test 2: Try to delete user without admin token
    print("\n2. Testing user deletion without admin token...")
    
    headers_no_admin = {
        "Authorization": f"Bearer some-non-admin-token",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.delete(
            f"{BASE_URL}/api/users/{TEST_USER_ID}",
            headers=headers_no_admin
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 403:
            print("✅ Correctly denied access for non-admin user")
            print(f"Error: {response.json()}")
        else:
            print(f"❌ Expected 403, got {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    
    # Test 3: Try to delete non-existent user
    print("\n3. Testing deletion of non-existent user...")
    
    fake_user_id = "00000000-0000-0000-0000-000000000000"
    
    try:
        response = requests.delete(
            f"{BASE_URL}/api/users/{fake_user_id}",
            headers=headers
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 404:
            print("✅ Correctly returned 404 for non-existent user")
            print(f"Error: {response.json()}")
        else:
            print(f"❌ Expected 404, got {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 Test completed!")
    print("\nNOTE: To run this test properly, you need to:")
    print("1. Start your FastAPI server")
    print("2. Replace ADMIN_TOKEN with a valid admin JWT token")
    print("3. Replace TEST_USER_ID with a real user ID you want to delete")
    print("4. Make sure you have admin privileges")


def create_test_user_for_deletion():
    """Helper function to create a test user that can be safely deleted"""
    
    BASE_URL = "http://localhost:8000"
    
    test_user_data = {
        "username": f"test_delete_user_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "email": f"test_delete_{datetime.now().strftime('%Y%m%d_%H%M%S')}@example.com",
        "mobile": f"+1234567{datetime.now().strftime('%H%M%S')}",
        "password": "testpassword123",
        "user_type": "student",
        "country": "Test Country"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/users/signup",
            json=test_user_data
        )
        
        if response.status_code == 200:
            user = response.json()
            print(f"✅ Created test user: {user['id']}")
            print(f"Username: {user['username']}")
            print(f"Email: {user['email']}")
            return user['id']
        else:
            print(f"❌ Failed to create test user: {response.status_code}")
            print(f"Error: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return None


if __name__ == "__main__":
    print("Admin Delete User Endpoint Test")
    print("Choose an option:")
    print("1. Create a test user for deletion")
    print("2. Run delete endpoint tests")
    print("3. Both (create user then test deletion)")
    
    choice = input("\nEnter your choice (1-3): ").strip()
    
    if choice == "1":
        create_test_user_for_deletion()
    elif choice == "2":
        test_delete_user_endpoint()
    elif choice == "3":
        user_id = create_test_user_for_deletion()
        if user_id:
            print(f"\n📝 Use this user ID for testing: {user_id}")
            input("Press Enter to continue with deletion test...")
            test_delete_user_endpoint()
    else:
        print("Invalid choice. Please run the script again.")
